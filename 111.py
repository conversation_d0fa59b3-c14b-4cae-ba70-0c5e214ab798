const axios = require('axios');
const https = require('https');
const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

class FnosSign {
    constructor(options = {}) {
        this.baseUrl = "https://club.fnnas.com";
        this.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Accept-Encoding": "gzip, deflate, br",
            "Referer": "https://club.fnnas.com/plugin.php?id=zqlj_sign&tb=today",
            "Connection": "keep-alive",
            "Sec-Ch-Ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": '"Windows"',
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-User": "?1",
            "Sec-Fetch-Dest": "document"
        };
        
        // 修改配置选项，分离功能 - 借鉴老版本配置
        this.config = {
            task: 'all',           // 'sign' 仅签到 | 'browse' 仅浏览 | 'all' 全部执行 | 'rush' 抢签到
            readTime: [15, 30],    // 阅读时间范围（秒）
            postCount: 1,          // 浏览帖子数量
            maxRetries: 3,         // 最大重试次数
            // 抢签到配置 - 借鉴老版本的参数
            rushMode: false,       // 是否启用抢签到模式
            concurrentRequests: 80, // 抢签到并发请求数量
            signRequestTimeout: 300, // 抢签到请求超时时间
            maxAdvanceTime: 100,   // 提前开始请求的最大时间（毫秒）
            advanceTimeOffset: 20, // 提前时间的附加时间（毫秒）
            signAttemptDuration: 8000, // 抢签到尝试的总时长（毫秒）
            batchRequestInterval: 30,  // 并发请求的间隔时间（毫秒）
            // 新增老版本的配置
            formhashTimeout: 5000,     // 获取formhash的超时时间
            formhashMaxRetries: 3,     // 获取formhash的最大重试次数
            formhashRetryInterval: 100, // 每次重试的间隔时间（毫秒）
            signUrlWarmupCount: 3,     // 预热签到URL的连接次数
            ...options
        };
        
        // 调整网络配置，借鉴老版本的高效配置
        this.client = axios.create({
            httpsAgent: new https.Agent({
                rejectUnauthorized: false,
                keepAlive: true,
                keepAliveMsecs: 5000,  // 借鉴老版本的5秒保持连接
                maxSockets: 50,        // 借鉴老版本的50个最大连接
                maxFreeSockets: 50     // 借鉴老版本的50个空闲连接
            }),
            timeout: this.config.formhashTimeout || 8000  // 借鉴老版本的超时配置
        });

        // 设置最大监听器数量
        require('events').EventEmitter.defaultMaxListeners = 0;

        // 添加签到锁定时间
        this.signLockTime = null;

        // 初始化数据库
        this.dbPath = path.join(__dirname, 'fnos.db');
        this.dbInitialized = false;
        this.initDatabase().then(() => {
            this.dbInitialized = true;
        }).catch(err => {
            console.error('数据库初始化失败:', err.message);
        });
    }

    async initDatabase() {
        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    console.error('数据库连接失败:', err.message);
                    reject(err);
                    return;
                }
                console.log('数据库连接成功');

                // 创建所有表
                this.createTables()
                    .then(() => resolve())
                    .catch(reject);
            });
        });
    }

    async createTables() {
        const tables = [
            // 创建签到记录表
            `CREATE TABLE IF NOT EXISTS sign_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sign_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                success BOOLEAN,
                reward INTEGER,          -- 获得的飞牛币
                rank INTEGER,            -- 签到排名
                network_delay INTEGER,   -- 网络延迟(ms)
                execution_time INTEGER,  -- 执行耗时(ms)
                message TEXT            -- 签到结果消息
            )`,

            // 创建浏览点赞记录表
            `CREATE TABLE IF NOT EXISTS browse_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                browse_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                post_id TEXT,           -- 帖子ID
                success BOOLEAN,
                read_time INTEGER,      -- 阅读时间(秒)
                like_success BOOLEAN,   -- 点赞是否成功
                message TEXT
            )`,

            // 创建用户状态记录表
            `CREATE TABLE IF NOT EXISTS user_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                check_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                username TEXT,
                coins INTEGER,          -- 飞牛币数量
                credits INTEGER,        -- 积分
                login_days INTEGER,     -- 登录天数
                sign_days INTEGER,      -- 累计签到天数
                continuous_days INTEGER, -- 连续签到天数
                sign_level TEXT         -- 签到等级
            )`,

            // 添加已点赞帖子记录表
            `CREATE TABLE IF NOT EXISTS liked_posts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                post_id TEXT UNIQUE,        -- 帖子ID，设为唯一
                like_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                title TEXT,                 -- 帖子标题
                author TEXT                 -- 帖子作者
            )`
        ];

        for (const sql of tables) {
            await new Promise((resolve, reject) => {
                this.db.run(sql, (err) => {
                    if (err) {
                        console.error('创建表失败:', err.message);
                        reject(err);
                    } else {
                        resolve();
                    }
                });
            });
        }
        console.log('数据库表初始化完成');
    }

    // 等待数据库初始化完成
    async waitForDatabase() {
        while (!this.dbInitialized) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }

    // 关闭数据库连接
    async closeDatabase() {
        return new Promise((resolve) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        console.log('关闭数据库时出错:', err.message);
                    } else {
                        console.log('数据库连接已关闭');
                    }
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }

    login(cookieString) {
        if (!cookieString) {
            throw new Error("Cookie不能为空");
        }
        this.client.defaults.headers.Cookie = cookieString;
    }

    async sign() {
        console.log("开始预热连接...");
        // 增加预热重试次数
        for (let i = 0; i < 3; i++) {
            try {
                await this.client.head(this.baseUrl);
                console.log("预热连接成功");
                break;
            } catch (e) {
                console.log(`第${i + 1}次预热连接失败${i < 2 ? '，重试中...' : ''}`);
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        // 添加时间同步检查
        let serverTimeOffset = 0;
        let networkDelay = 0;
        
        try {
            const startTime = Date.now();
            const { headers } = await this.client.head(this.baseUrl);
            const endTime = Date.now();
            networkDelay = Math.floor((endTime - startTime) / 2);
            
            if (headers.date) {
                const serverTime = new Date(headers.date).getTime() + networkDelay;
                serverTimeOffset = serverTime - Date.now();
                console.log(`服务器时间偏移: ${serverTimeOffset}ms`);
                console.log(`网络延迟: ${networkDelay}ms`);
            }
        } catch (e) {
            console.log("获取服务器时间失败，使用本地时间");
        }

        // 先检查签到状态
        console.log("检查签到状态...");
        const signStatus = await this.getSignStatus();
        if (signStatus && signStatus.last_time) {
            const lastSignDate = new Date(signStatus.last_time);
            const today = new Date();
            const isToday = lastSignDate.toDateString() === today.toDateString();
            
            if (isToday) {
                console.log("今天已经签到过了");
                return {
                    success: true,
                    message: "今天已经签到过了",
                    time: new Date().toLocaleString(),
                    attempts: 0,
                    status: signStatus
                };
            }
        }

        // 获取sign参数 (添加重试逻辑)
        console.log("开始获取sign参数...");
        let signParam;
        for (let i = 0; i < 3; i++) {
            try {
                const { data: text } = await this.client.get(
                    `${this.baseUrl}/plugin.php?id=zqlj_sign&tb=today`,
                    { 
                        headers: {
                            ...this.headers,
                            'Referer': 'https://club.fnnas.com/plugin.php?id=zqlj_sign&tb=today'
                        },
                        timeout: 5000
                    }
                );
                
                // 尝试从页面中提取sign参数
                const signMatch = text.match(/sign=([a-f0-9]+)/);
                if (signMatch) {
                    signParam = signMatch[1];
                    console.log("获取到的 sign 参数:", signParam);
                    break;
                }
            } catch (e) {
                console.log(`第${i + 1}次获取sign参数失败，${i < 2 ? '正在重试...' : '放弃重试'}`);
                if (i === 2) {
                    throw new Error("获取 sign 参数失败");
                }
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        // 如果没有找到sign参数，使用默认值
        if (!signParam) {
            signParam = "142f0c06"; // 使用你抓包中的默认值
            console.log("使用默认 sign 参数:", signParam);
        }

        const signUrl = `${this.baseUrl}/plugin.php?id=zqlj_sign&sign=${signParam}`;
        let attemptCount = 0;
        let success = false;
        let result = null;

        // 预热签到URL的连接
        console.log("预热签到URL连接...");
        for (let i = 0; i < 3; i++) {
            try {
                await this.client.head(signUrl);
                console.log("签到URL预热成功");
                break;
            } catch (e) {
                console.log(`第${i + 1}次签到URL预热失败${i < 2 ? '，重试中...' : ''}`);
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        // 移除时间限制相关代码，直接开始签到
        console.log("开始执行签到...");

        // 创建一个控制器用于取消请求
        const controller = new AbortController();

        // 准备10个并发请求
        const requests = Array(10).fill().map(() => ({
            url: signUrl,
            headers: {
                ...this.headers,
                'Referer': 'https://club.fnnas.com/plugin.php?id=zqlj_sign&tb=today',
                'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-User': '?1',
                'Sec-Fetch-Dest': 'document'
            },
            timeout: 5000  // 增加超时时间到5秒
        }));

        // 立即并发发送所有请求
        const startTime = Date.now();
        const endTime = startTime + 5000; // 最多尝试5秒
        
        while (!success && Date.now() < endTime) {
            // 同时发送10个请求
            const batchRequests = requests.map(async (req) => {
                attemptCount++;
                try {
                    const { data } = await this.client.get(req.url, { 
                        headers: req.headers,
                        timeout: req.timeout,
                        signal: controller.signal
                    });
                    
                    if (data.includes("恭喜您，打卡成功")) {
                        const rewardMatch = data.match(/获得(\d+)飞牛币/);
                        return {
                            success: true,
                            message: rewardMatch ? `签到成功，获得${rewardMatch[1]}飞牛币` : "签到成功"
                        };
                    }
                    return null;
                } catch (e) {
                    return null;
                }
            });

            // 等待任意一个请求成功
            const results = await Promise.race([
                Promise.all(batchRequests),
                new Promise(resolve => setTimeout(resolve, 100)) // 100ms后继续下一批
            ]);

            if (Array.isArray(results)) {
                const successResult = results.find(r => r?.success);
                if (successResult) {
                    success = true;
                    result = successResult;
                    controller.abort();
                    break;
                }
            }
        }

        const status = await this.getSignStatus();
        
        return {
            success: result?.success || false,
            message: result?.message || "签到失败",
            time: new Date().toLocaleString(),
            status,
            attempts: attemptCount
        };
    }

    // 抢签到方法 - 借鉴老版本优化
    async rushSign() {
        console.log("=== 抢签到模式启动 ===");

        // 预热连接
        console.log("开始预热连接...");
        try {
            await this.client.head(this.baseUrl);
            console.log("预热连接成功");
        } catch (e) {
            console.log("预热连接失败，继续执行");
        }

        // 添加时间同步检查
        let serverTimeOffset = 0;
        let networkDelay = 0;

        try {
            const startTime = Date.now();
            const { headers } = await this.client.head(this.baseUrl);
            const endTime = Date.now();
            networkDelay = Math.floor((endTime - startTime) / 2);

            if (headers.date) {
                const serverTime = new Date(headers.date).getTime() + networkDelay;
                serverTimeOffset = serverTime - Date.now();
                console.log(`服务器时间偏移: ${serverTimeOffset}ms`);
                console.log(`网络延迟: ${networkDelay}ms`);
            }
        } catch (e) {
            console.log("获取服务器时间失败，使用本地时间");
        }

        // 获取sign参数 (添加重试逻辑)
        console.log("开始获取sign参数...");
        let signParam;
        for (let i = 0; i < 3; i++) {
            try {
                const { data: text } = await this.client.get(
                    `${this.baseUrl}/plugin.php?id=zqlj_sign`,
                    {
                        headers: this.headers,
                        timeout: 5000
                    }
                );

                // 使用老版本的formhash匹配方式
                const formhashMatch = text.match(/formhash=(\w+)['"]/)
                if (formhashMatch) {
                    signParam = formhashMatch[1];
                    console.log("获取到的 sign 参数:", signParam);
                    break;
                }
            } catch (e) {
                console.log(`第${i + 1}次获取sign参数失败，${i < 2 ? '正在重试...' : '放弃重试'}`);
                if (i === 2) {
                    throw new Error("获取 sign 参数失败");
                }
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }

        const signUrl = `${this.baseUrl}/plugin.php?id=zqlj_sign&sign=${signParam}`;
        let attemptCount = 0;
        let success = false;
        let result = null;

        // 提前准备好请求对象并预热连接
        const requests = Array(this.config.concurrentRequests).fill().map(() => ({
            url: signUrl,
            headers: this.headers,
            timeout: this.config.signRequestTimeout
        }));

        // 预热签到URL的连接
        try {
            const warmupPromises = Array(this.config.signUrlWarmupCount || 3).fill().map(() => this.client.head(signUrl));
            await Promise.all(warmupPromises);
            console.log("签到URL预热成功");
        } catch (e) {
            console.log("预热签到连接失败，继续执行");
        }

        // 根据网络延迟动态调整提前时间
        const advanceTime = Math.min(networkDelay + this.config.advanceTimeOffset, this.config.maxAdvanceTime);
        console.log(`根据网络延迟(${networkDelay}ms)，设置提前${advanceTime}ms开始请求`);

        // 等待接近0点 - 借鉴老版本的时间控制逻辑
        const now = new Date();
        const targetTime = new Date(now);
        targetTime.setHours(24, 0, 0, 0);

        // 计算当前是否接近目标时间
        const currentSeconds = now.getSeconds();
        const isNearTarget = currentSeconds >= 50 && currentSeconds <= 59;

        if (!isNearTarget) {
            console.log("当前不在目标时间范围内，脚本将退出");
            return {
                success: false,
                message: "不在目标执行时间范围内",
                time: new Date().toLocaleString(),
                attempts: 0
            };
        }

        // 计算等待时间
        const waitTime = targetTime.getTime() - now.getTime() - advanceTime;
        if (waitTime > 0) {
            console.log(`等待${waitTime}ms后开始抢签到...`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }

        // 创建一个控制器用于取消请求
        const controller = new AbortController();

        // 立即并发发送所有请求 - 借鉴老版本的策略
        const startTime = Date.now();
        const endTime = startTime + this.config.signAttemptDuration;

        console.log(`开始抢签到，使用${this.config.concurrentRequests}个并发请求...`);

        while (!success && Date.now() < endTime) {
            // 同时发送所有请求
            const batchRequests = requests.map(async (req) => {
                attemptCount++;
                try {
                    const { data } = await this.client.get(req.url, {
                        headers: req.headers,
                        timeout: req.timeout,
                        signal: controller.signal
                    });

                    if (data.includes("恭喜您，打卡成功")) {
                        const rewardMatch = data.match(/获得(\d+)飞牛币/);
                        return {
                            success: true,
                            message: rewardMatch ? `抢签到成功，获得${rewardMatch[1]}飞牛币` : "抢签到成功"
                        };
                    }
                    return null;
                } catch (e) {
                    return null;
                }
            });

            // 等待任意一个请求成功
            const results = await Promise.race([
                Promise.all(batchRequests),
                new Promise(resolve => setTimeout(resolve, this.config.batchRequestInterval))
            ]);

            if (Array.isArray(results)) {
                const successResult = results.find(r => r?.success);
                if (successResult) {
                    success = true;
                    result = successResult;
                    controller.abort();
                    break;
                }
            }
        }

        const status = await this.getSignStatus();

        return {
            success: result?.success || false,
            message: result?.message || "抢签到失败",
            time: new Date().toLocaleString(),
            status,
            attempts: attemptCount
        };
    }

    async getSignStatus() {
        try {
            const { data: text } = await this.client.get(
                `${this.baseUrl}/plugin.php?id=zqlj_sign`,
                { headers: this.headers }
            );

            const status = {
                last_time: "",
                month_days: "",
                continuous_days: "",
                total_days: "",
                total_reward: "",
                last_reward: "",
                level: ""
            };

            const patterns = {
                last_time: /最近打卡：(.*?)<\/li>/,
                month_days: /本月打卡：(\d+)天/,
                continuous_days: /连续打卡：(\d+)天/,
                total_days: /累计打卡：(\d+)天/,
                total_reward: /累计奖励：(\d+)飞牛币/,
                last_reward: /最近奖励：(\d+)飞牛币/,
                level: /当前打卡等级：(.*?)<\/li>/
            };

            for (const [key, pattern] of Object.entries(patterns)) {
                const match = text.match(pattern);
                if (match) status[key] = match[1];
            }

            return status;
        } catch (e) {
            return null;
        }
    }

    async getPostList(page = 1) {
        try {
            // 生成一个100-14900范围内的随机帖子ID数组
            const tids = [];
            const minTid = 100;
            const maxTid = 31323;
            const totalPosts = 200; // 保持与原来相同的帖子数量

            while (tids.length < totalPosts) {
                const randomTid = Math.floor(Math.random() * (maxTid - minTid + 1)) + minTid;
                if (!tids.includes(randomTid)) {
                    tids.push(randomTid);
                }
            }

            console.log(`生成 ${tids.length} 个随机帖子ID (范围: ${minTid}-${maxTid})`);
            return { tids, maxPage: 1 };
            
        } catch(e) {
            console.log('获取帖子列表失败:', e.message);
            return { tids: [], maxPage: 1 };
        }
    }

    // 检查帖子是否已点赞
    async isPostLiked(postId) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT 1 FROM liked_posts WHERE post_id = ?',
                [postId],
                (err, row) => {
                    if (err) {
                        console.log('查询帖子点赞状态失败:', err.message);
                        resolve(false);  // 查询失败时假设未点赞
                    } else {
                        resolve(!!row);  // 有记录则表示已点赞
                    }
                }
            );
        });
    }

    // 记录已点赞的帖子
    async recordLikedPost(postId, title = '', author = '') {
        return new Promise((resolve, reject) => {
            const now = new Date().toISOString(); // 使用 ISO 格式的时间
            this.db.run(
                'INSERT OR IGNORE INTO liked_posts (post_id, title, author, like_time) VALUES (?, ?, ?, ?)',
                [postId, title, author, now],
                (err) => {
                    if (err) {
                        console.log('记录点赞帖子失败:', err.message);
                        reject(err);
                    } else {
                        resolve();
                    }
                }
            );
        });
    }

    // 修改访问点赞方法
    async visitAndLike() {
        const visitedPosts = [];
        try {
            // 先访问首页
            await this.client.get(this.baseUrl, { headers: this.headers });
            console.log('访问首页...');
            await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 1000));
            
            // 获取随机帖子列表
            const { tids } = await this.getPostList(1);
            if (tids.length === 0) {
                return {
                    success: false,
                    message: '没有找到可访问的帖子',
                    visitedPosts: []
                };
            }

            // 过滤出未点赞的帖子
            console.log('开始过滤未点赞的帖子...');
            const unlikedTids = [];
            for (const [index, tid] of tids.entries()) {
                if (index % 20 === 0 && index > 0) {
                    console.log(`  ...已扫描 ${index}/${tids.length} 个帖子`);
                }
                try {
                    // 先检查帖子是否存在
                    await this.client.head(
                        `${this.baseUrl}/forum.php?mod=viewthread&tid=${tid}`,
                        { 
                            headers: this.headers,
                            validateStatus: function (status) {
                                return status < 400; // 接受小于400的状态码
                            }
                        }
                    );
                    
                    const isLiked = await this.isPostLiked(tid.toString());
                    if (!isLiked) {
                        unlikedTids.push(tid);
                        if (unlikedTids.length >= 10) {
                            console.log('已找到10个未点赞的帖子，停止扫描。');
                            break; // 找到10个未点赞的帖子就停止
                        }
                    }
                } catch(e) {
                    // 帖子不存在或无法访问，跳过
                    continue;
                }
            }
            console.log(`过滤完成，共找到 ${unlikedTids.length} 个可点赞的帖子。`);

            if (unlikedTids.length === 0) {
                return {
                    success: false,
                    message: '所有随机获取的帖子都已点赞过或无法访问',
                    visitedPosts: []
                };
            }

            // 获取 formhash
            const { data } = await this.client.get(
                `${this.baseUrl}/forum.php?mod=forumdisplay&fid=4`,
                { headers: this.headers }
            );
            const formhashMatch = data.match(/formhash=(\w+)['"]/);
            if (!formhashMatch) {
                throw new Error('获取formhash失败');
            }
            const formhash = formhashMatch[1];

            let retryCount = 0;
            let success = false;

            while (!success && retryCount < this.config.maxRetries) {
                const randomTid = unlikedTids[Math.floor(Math.random() * unlikedTids.length)];
                try {
                    console.log(`\n正在访问帖子 ${randomTid}...`);
                    const { data: postData } = await this.client.get(
                        `${this.baseUrl}/forum.php?mod=viewthread&tid=${randomTid}`,
                        { headers: this.headers }
                    );

                    // 提取帖子标题和作者
                    const titleMatch = postData.match(/<title>(.*?)<\/title>/);
                    const authorMatch = postData.match(/作者:\s*<a[^>]*>([^<]+)<\/a>/);
                    const title = titleMatch ? titleMatch[1] : '';
                    const author = authorMatch ? authorMatch[1] : '';

                    console.log(`帖子 ${randomTid} 浏览成功`);
                    
                    const [minTime, maxTime] = this.config.readTime;
                    const readTime = (minTime + Math.random() * (maxTime - minTime)) * 1000;
                    console.log(`阅读帖子 ${Math.floor(readTime/1000)} 秒...`);
                    await new Promise(resolve => setTimeout(resolve, readTime));
                    
                    console.log(`正在点赞帖子 ${randomTid}...`);
                    const response = await this.client.get(
                        `${this.baseUrl}/forum.php?mod=misc&action=recommend&do=add&tid=${randomTid}&hash=${formhash}`,
                        {
                            headers: {
                                ...this.headers,
                                'X-Requested-With': 'XMLHttpRequest',
                                'Referer': `${this.baseUrl}/forum.php?mod=viewthread&tid=${randomTid}`
                            },
                            maxRedirects: 5
                        }
                    );
                    
                    if (response.status === 200) {
                        console.log(`帖子 ${randomTid} 点赞成功`);
                        // 记录点赞成功的帖子
                        await this.recordLikedPost(randomTid, title, author);
                        visitedPosts.push(randomTid);
                        success = true;
                    }
                } catch(e) {
                    console.log(`帖子 ${randomTid} 处理失败: ${e.message}`);
                    retryCount++;
                    if (retryCount < this.config.maxRetries) {
                        console.log(`尝试下一个帖子... (${retryCount}/${this.config.maxRetries})`);
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                }
            }

            return {
                success: visitedPosts.length > 0,
                message: visitedPosts.length > 0 ? 
                    `成功访问并点赞 ${visitedPosts.length} 个帖子` : 
                    '未能成功访问任何帖子',
                visitedPosts
            };

        } catch(e) {
            console.log('浏览点赞任务失败:', e.message);
            return {
                success: false,
                message: e.message,
                visitedPosts
            };
        }
    }

    // 修改检查浏览时间的方法
    async canVisit() {
        return new Promise((resolve, reject) => {
            // 使用 strftime 来获取时间戳
            this.db.get(`
                SELECT strftime('%s', like_time) as timestamp
                FROM liked_posts 
                ORDER BY like_time DESC 
                LIMIT 1
            `, (err, row) => {
                if (err) {
                    console.log('查询上次点赞时间失败:', err.message);
                    resolve(true);  // 出错时允许执行
                    return;
                }
                
                if (!row) {
                    resolve(true);  // 没有记录时允许执行
                    return;
                }

                const lastTime = parseInt(row.timestamp) * 1000; // 转换为毫秒
                const now = Date.now();
                const timeDiff = now - lastTime;
                const minutesDiff = Math.floor(timeDiff / (60 * 1000));
                
                console.log(`距离上次点赞已过 ${minutesDiff} 分钟`);
                // 检查是否过了10分钟
                resolve(timeDiff >= 10 * 60 * 1000);
            });
        });
    }

    // 修改主执行方法
    async executeAll() {
        const startTime = new Date();

        // 等待数据库初始化完成
        await this.waitForDatabase();

        try {
            let signResult = null;
            let browseResult = null;
            
            // 根据配置执行相应任务
            if (this.config.task === 'rush') {
                // 抢签到模式
                signResult = await this.rushSign();
                console.log(`状态: ${signResult.message}`);
                
                if (signResult.status) {
                    console.log('\n签到统计:');
                    console.log(`  ├─ 最近打卡: ${signResult.status.last_time}`);
                    console.log(`  ├─ 本月打卡: ${signResult.status.month_days}天`);
                    console.log(`  ├─ 连续打卡: ${signResult.status.continuous_days}天`);
                    console.log(`  ├─ 累计打卡: ${signResult.status.total_days}天`);
                    console.log(`  ├─ 累计奖励: ${signResult.status.total_reward}飞牛币`);
                    console.log(`  ├─ 最近奖励: ${signResult.status.last_reward}飞牛币`);
                    console.log(`  └─ 当前等级: ${signResult.status.level}`);
                }
            } else if (this.config.task === 'all' || this.config.task === 'sign') {
                // 普通签到模式
                signResult = await this.sign();
                console.log(`状态: ${signResult.message}`);
                
                if (signResult.status) {
                    console.log('\n签到统计:');
                    console.log(`  ├─ 最近打卡: ${signResult.status.last_time}`);
                    console.log(`  ├─ 本月打卡: ${signResult.status.month_days}天`);
                    console.log(`  ├─ 连续打卡: ${signResult.status.continuous_days}天`);
                    console.log(`  ├─ 累计打卡: ${signResult.status.total_days}天`);
                    console.log(`  ├─ 累计奖励: ${signResult.status.total_reward}飞牛币`);
                    console.log(`  ├─ 最近奖励: ${signResult.status.last_reward}飞牛币`);
                    console.log(`  └─ 当前等级: ${signResult.status.level}`);
                }
            }
            
            if (this.config.task === 'all' || this.config.task === 'browse') {
                // 检查是否可以执行浏览任务
                const canVisitNow = await this.canVisit();
                if (canVisitNow) {
                    console.log('\n【浏览点赞任务】');
                    console.log('-'.repeat(30));
                    browseResult = await this.visitAndLike();
                    if (browseResult.success) {
                        await this.recordBrowseResult({
                            postId: browseResult.visitedPosts[0],
                            success: true,
                            readTime: this.config.readTime[0],
                            likeSuccess: true,
                            message: browseResult.message
                        });
                    }
                    if (browseResult.visitedPosts.length > 0) {
                        console.log('\n浏览统计:');
                        browseResult.visitedPosts.forEach((tid, index) => {
                            console.log(`  └─ 帖子${index + 1}: ${tid}`);
                        });
                    }
                } else {
                    console.log('\n【浏览点赞任务】');
                    console.log('-'.repeat(30));
                    console.log('距离上次点赞未满10分钟，跳过执行');
                }
            }

            // 获取用户信息
            console.log('\n【用户信息】');
            console.log('-'.repeat(30));
            const userInfo = await this.getUserInfo();
            if (userInfo) {
                console.log(`  ├─ 用户名: ${userInfo.username}`);
                console.log(`  ├─ UID: ${userInfo.uid}`);
                console.log(`  ├─ 在线时间: ${userInfo.onlineTime}`);
                console.log(`  ├─ 注册时间: ${userInfo.regTime}`);
                console.log(`  ├─ 积分: ${userInfo.credits}`);
                console.log(`  ├─ 飞牛币: ${userInfo.coins}`);
                console.log(`  ├─ 登录天数: ${userInfo.loginDays}`);
                console.log(`  └─ 今日打卡排名: ${userInfo.todayRank || '未打卡'}`);
            }

            // 简化任务完成统计
            const endTime = new Date();
            const duration = Math.round((endTime - startTime) / 1000);
            
            return {
                signResult: signResult || { success: false, message: '未执行签到' },
                browseResult: browseResult || { success: false, message: '未执行浏览' },
                userInfo,
                duration
            };
            
        } catch (error) {
            console.error(`执行失败: ${error.message}`);
            throw error;
        }
    }

    async getUserInfo() {
        try {
            // 获取基本用户信息
            const { data: profileText } = await this.client.get(
                `${this.baseUrl}/home.php?mod=space&do=profile&from=space`,
                { headers: this.headers }
            );

            // 从页面提取当前用户名
            const username = (profileText.match(/用户名<\/em>([^<]+)/) || [])[1];
            
            // 查找用户的排名（最多查找6页）
            let todayRank = null;
            if (username) {
                for (let page = 1; page <= 600; page++) {
                    const { data: rankText } = await this.client.get(
                        `${this.baseUrl}/plugin.php?id=zqlj_sign&tb=today&page=${page}#tblist`,
                        { headers: this.headers }
                    );
                    
                    // 在当前页查找用户
                    const userRow = rankText.match(new RegExp(`<td>\\d+</td>\\s*<td><a[^>]*>${username}</a></td>`));
                    if (userRow) {
                        // 直接从表格行获取排名
                        const rankMatch = userRow[0].match(/^<td>(\d+)<\/td>/);
                        if (rankMatch) {
                            todayRank = rankMatch[1];
                            break;
                        }
                    }
                }
            }

            const info = {
                username: username,
                uid: (profileText.match(/UID<\/em>(\d+)/) || [])[1],
                onlineTime: (profileText.match(/在线时间<\/em>([^<]+)/) || [])[1],
                regTime: (profileText.match(/注册时间<\/em>([^<]+)/) || [])[1],
                credits: (profileText.match(/积分<\/em>(\d+)/) || [])[1],
                coins: (profileText.match(/飞牛币<\/em>(\d+)/) || [])[1],
                loginDays: (profileText.match(/登陆天数<\/em>(\d+)/) || [])[1],
                todayRank: todayRank  // 添加今日排名
            };

            return info;
        } catch (e) {
            console.log('获取用户信息失败:', e.message);
            return null;
        }
    }

    // 记录签到结果
    async recordSignResult(result) {
        return new Promise((resolve, reject) => {
            this.db.run(`
                INSERT INTO sign_records (
                    success, reward, rank, network_delay, 
                    execution_time, message
                ) VALUES (?, ?, ?, ?, ?, ?)
            `, [
                result.success,
                result.reward || 0,
                result.rank || null,
                result.networkDelay || 0,
                result.executionTime || 0,
                result.message
            ], (err) => {
                if (err) reject(err);
                else resolve();
            });
        });
    }

    // 记录浏览点赞结果
    async recordBrowseResult(result) {
        return new Promise((resolve, reject) => {
            this.db.run(`
                INSERT INTO browse_records (
                    post_id, success, read_time, 
                    like_success, message
                ) VALUES (?, ?, ?, ?, ?)
            `, [
                result.postId,
                result.success,
                result.readTime || 0,
                result.likeSuccess || false,
                result.message
            ], (err) => {
                if (err) reject(err);
                else resolve();
            });
        });
    }

    // 更新用户状态
    async updateUserStats(stats) {
        return new Promise((resolve, reject) => {
            this.db.run(`
                INSERT INTO user_stats (
                    username, coins, credits, login_days,
                    sign_days, continuous_days, sign_level
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            `, [
                stats.username,
                stats.coins,
                stats.credits,
                stats.loginDays,
                stats.signDays,
                stats.continuousDays,
                stats.signLevel
            ], (err) => {
                if (err) reject(err);
                else resolve();
            });
        });
    }

    // 获取签到统计
    async getSignStats() {
        return new Promise((resolve, reject) => {
            this.db.get(`
                SELECT 
                    COUNT(*) as total_signs,
                    SUM(reward) as total_rewards,
                    AVG(network_delay) as avg_delay,
                    MIN(rank) as best_rank
                FROM sign_records 
                WHERE success = 1
            `, (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });
    }
}

// 修改 main 函数，添加命令行参数处理
async function main() {
    // 获取命令行参数
    const args = process.argv.slice(2);
    let task = 'all';  // 默认执行所有任务

    // 解析命令行参数
    if (args.includes('--sign')) {
        task = 'sign';
    } else if (args.includes('--browse')) {
        task = 'browse';
    } else if (args.includes('--rush')) {
        task = 'rush';
    }

    const fnos = new FnosSign({
        task: task,
        readTime: [15, 30],
        postCount: 1,
        // 抢签到配置
        rushMode: task === 'rush',
        concurrentRequests: 80,
        signRequestTimeout: 300,
        maxAdvanceTime: 100,
        advanceTimeOffset: 20,
        signAttemptDuration: 8000,
        batchRequestInterval: 30
    });

    const cookieString = "Hm_lvt_f44c77c038dd3fcfea6a8bcb439067e5=**********,**********,**********; HMACCOUNT=70BB84CE4124CFAE; pvRK_2132_saltkey=xzHII6ON; pvRK_2132_lastvisit=**********; pvRK_2132_sid=BmqWZ4; pvRK_2132_sendmail=1; pvRK_2132_seccodecSBmqWZ4=2.c479789a5894fc898c; pvRK_2132_ulastactivity=ae50ExJXjgPt5fVr8TDBASSAa%2B3KBTWsGtyPw75SJ5Eh%2FuoPs6NQ; pvRK_2132_auth=f6d3yxHiAlWgFgKmy8bHywte31%2FMikPmmZ8tVIQADPiPQjD3RMXnLQWjXbl8i9z%2BetcjCoELNwBSp4KRCn8jZInT5w; pvRK_2132_lastcheckfeed=37561%7C1754173942; pvRK_2132_checkfollow=1; pvRK_2132_lip=**************%2C1754173937; pvRK_2132_myrepeat_rr=R0; pvRK_2132_checkpm=1; pvRK_2132_lastact=**********%09misc.php%09patch; Hm_lpvt_f44c77c038dd3fcfea6a8bcb439067e5=**********";

    try {
        fnos.login(cookieString);
        await fnos.executeAll();
    } catch (error) {
        console.error('程序执行失败:', error.message);
    }
}

// 如果直接运行此文件则执行main()
if (require.main === module) {
    main();
}

// 导出类以供其他模块使用
module.exports = FnosSign;     